import AvatarSDK from "./core/AvatarSDK"
import { BaseLoader } from "./loader/BaseLoader"
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader"
import { SceneManager } from "./core/SceneManager"
import AvatarView from "./components/AvatarView.vue"

export function createAvatarSDK(options = {}) {

    const sdk = new AvatarSDK(options)

    return sdk
}

export {
    AvatarSDK,
    SceneManager,
    BaseLoader,
    FBXLoader,
    AvatarView,
}
