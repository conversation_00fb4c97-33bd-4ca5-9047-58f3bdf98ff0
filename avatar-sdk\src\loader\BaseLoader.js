import { EventEmitter } from "../core/EventEmitter";;

/**
 * 资源loader的基类
 */
export class BaseLoader extends EventEmitter {
    
    constructor() {
        super();
    }



    /**
     * 根据路径去加载
     * @param {string} path - 资源路径
     * @returns {Promise<THREE.Object3D>} Loaded model
     */
    async load(path) {
        throw new Error('Method not implemented');
    }

    /**
     * @param {ArrayBuffer|string} data
     * @param {Object} options
     * @returns {Promise<THREE.Object3D>}
     */
    async parse(data) {
        throw new Error('Method not implemented');
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.clear();
    }
}
