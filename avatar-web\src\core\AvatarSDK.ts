import * as THREE from 'three';
import { ModelLoader } from '../loader/ModelLoader';
import { EventEmitter } from './EventEmitter';
import { SceneManager } from './SceneManager';
import { AnimationLoader } from '../loader/AnimationLoader';
import { AvatarSDKOptions, ModelLoadOptions, AnimationLoadOptions } from '../types';

export default class AvatarSDK extends EventEmitter {
    private options: AvatarSDKOptions;
    private model: THREE.Object3D | null = null;
    private initialized: boolean = false;
    private modelLoader: ModelLoader;
    private animationLoader: AnimationLoader;
    private sceneManager: SceneManager | null = null;

    constructor(options: AvatarSDKOptions = {}) {
        super();
        this.options = {
            debug: false,
            showControls: false,
            ...options
        };
        this.modelLoader = new ModelLoader();
        this.animationLoader = new AnimationLoader();
    }

    async init(canvas: HTMLCanvasElement): Promise<void> {
        if (this.initialized) {
            console.log("AvatarSDK already initialized");
            return;
        }

        try {
            this.sceneManager = new SceneManager(canvas, {
                debug: this.options.debug,
                showControls: this.options.showControls,
                fov: this.options.fov,
                near: this.options.near,
                far: this.options.far,
                antialias: this.options.antialias,
                alpha: this.options.alpha,
                autoResize: this.options.autoResize
            });
            this.sceneManager.start();
            this.initialized = true;
        } catch (error) {
            console.error("Failed to initialize AvatarSDK:", error);
            throw error;
        }
    }

    async loadModel(url: string, options: ModelLoadOptions = {}): Promise<THREE.Object3D | undefined> {
        try {
            this.model = await this.modelLoader.load(url, options);
            if (this.sceneManager) {
                this.sceneManager.add(this.model);
            }
            console.log("load model success", this.model.position);
            return this.model;
        } catch (error) {
            console.log("load model error", error);
            throw error;
        }
    }

    async playAnimation(url: string, options: AnimationLoadOptions = {}): Promise<void> {
        try {
            const animObj = await this.animationLoader.load(url, options);
            console.log("animObj:", animObj);
            
            if (this.model === null) {
                console.log("模型为空,请先加载模型");
                return;
            }
            
            if (this.sceneManager) {
                this.sceneManager.addAndPlayAnimation(animObj, this.model);
            }
        } catch (error) {
            console.log("load anim error", error);
            throw error;
        }
    }

    dispose(): void {
        if (this.sceneManager) {
            this.sceneManager.dispose();
        }
        this.modelLoader.dispose();
        this.animationLoader.dispose();
        this.clear();
    }

    getModel(): THREE.Object3D | null {
        return this.model;
    }

    getSceneManager(): SceneManager | null {
        return this.sceneManager;
    }

    isInitialized(): boolean {
        return this.initialized;
    }
}
