<template>
    <div class="avatar-viewer">
        <canvas ref="avatarViewCanvas" class="avatar-view-canvas"></canvas>
    </div>
</template>

<script setup>
import { createAvatarSDK } from '../index.js';
import { ref, onMounted, onUnmounted, watch } from 'vue'

const avatarViewCanvas = ref(null);

const props = defineProps({
    debug: {
        type: Boolean,
        default: false
    },
    showControls: {
        type: Boolean,
        default: true
    },
    modelUrl: {
        type: String,
        default: ''
    },
    animationUrl: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['sdk-ready', 'model-loaded', 'animation-loaded', 'error']);

/** @type {import('../core/AvatarSDK.js').default} */
let avatarSDK = null;

onMounted(async () => {
    try {
        // Wait for next tick to ensure canvas is properly sized
        await new Promise(resolve => setTimeout(resolve, 0));

        avatarSDK = createAvatarSDK({
            debug: props.debug,
            showControls: props.showControls
        });

        await avatarSDK.init(avatarViewCanvas.value);
        emit('sdk-ready', avatarSDK);

        // Load model if provided
        if (props.modelUrl) {
            await loadModel(props.modelUrl);
        }

        // Load animation if provided
        if (props.animationUrl) {
            await loadAnimation(props.animationUrl);
        }
    } catch (error) {
        console.error("init avatar error:", error);
        emit('error', error);
    }
});

onUnmounted(() => {
    if (avatarSDK) {
        avatarSDK.dispose?.();
    }
});

// Watch for prop changes
watch(() => props.modelUrl, async (newUrl) => {
    if (newUrl && avatarSDK) {
        await loadModel(newUrl);
    }
});

watch(() => props.animationUrl, async (newUrl) => {
    if (newUrl && avatarSDK) {
        await loadAnimation(newUrl);
    }
});

async function loadModel(url) {
    try {
        const model = await avatarSDK.loadModel(url);
        emit('model-loaded', model);
    } catch (error) {
        console.error("load model error:", error);
        emit('error', error);
    }
}

async function loadAnimation(url) {
    try {
        await avatarSDK.playAnimation(url);
        emit('animation-loaded');
    } catch (error) {
        console.error("load animation error:", error);
        emit('error', error);
    }
}

// Expose methods for parent components
defineExpose({
    loadModel,
    loadAnimation,
    getSDK: () => avatarSDK
});

</script>

<style scoped>
.avatar-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.avatar-view-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>
