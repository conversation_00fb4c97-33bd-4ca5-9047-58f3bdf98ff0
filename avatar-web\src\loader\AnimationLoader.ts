import * as THREE from 'three';
import { BaseLoader } from './BaseLoader';
import { CustomFBXLoader } from './CustomFBXLoader';
import { AnimationLoadOptions } from '../types';

export class AnimationLoader extends BaseLoader {
    private loader: CustomFBXLoader;

    constructor(options: AnimationLoadOptions = {}) {
        super(options);
        this.loader = new CustomFBXLoader();
    }

    async load(path: string, options: AnimationLoadOptions = {}): Promise<THREE.Object3D> {
        try {
            const animObj = await this.loader.load(path, options);
            return animObj;
        } catch (error) {
            throw error;
        }
    }

    async parse(data: ArrayBuffer | string, options: AnimationLoadOptions = {}): Promise<THREE.Object3D> {
        return this.loader.parse(data, options);
    }
}
