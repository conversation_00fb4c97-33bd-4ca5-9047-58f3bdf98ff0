import { EventEmitter } from './EventEmitter';
import { AnimationLoader } from '../loader/AnimationLoader';

export class AnimationManager extends EventEmitter {
    constructor() {
        super();
        this.loader = new AnimationLoader();
    }

    async play(animPath, options = {}) {
        try {
            await this.loader.load(animPath, options);
        } catch (error) {

        }


    }

}
