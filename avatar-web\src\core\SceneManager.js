import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { EventEmitter } from './EventEmitter';

/**
 * 主要用于创建scene以及相机还有动画，加入模型，相当于和场景管理相关的都在此类实现
 */
export class SceneManager extends EventEmitter {
  /**
   * @param {HTMLCanvasElement} canvas - 用于渲染的canvas，通过html的布局传入进来
   * @param {Object} options - 一些可选项
   */
  constructor(canvas, options = {}) {
    super();

    this.canvas = canvas;
    this.options = {
      antialias: true,
      alpha: true,
      autoResize: true,
      ...options
    };

    this.clock = new THREE.Clock();
    
    //在定义一个变量的时候，如果没有显示直接new出来，可以通过type来指定其类型，这样方便调用其函数
    /** @type {THREE.AnimationMixer} */
    this.curAnimMixer = null;
    /** @type {THREE.AnimationClip} */
    this.curAnimClip = null;
     /** @type {THREE.AnimationAction} */
    this.curAnimAction = null;
  
    this.animationMixers = [];
    this.isRunning = false;

    this._initScene();
    this._initCamera();
    this._initRenderer();
    this._initLights();
    this._initControls();

    if (this.options.autoResize) {
      this._initResizeHandler();
    }
  }


  _initScene() {
    this.scene = new THREE.Scene();

    if (this.options.debug) {
      const axesHelper = new THREE.AxesHelper(5);
      this.scene.add(axesHelper);
    }

  }


  _initCamera() {
    const aspectRatio = this.canvas.clientWidth / this.canvas.clientHeight;
    this.camera = new THREE.PerspectiveCamera(
      this.options.fov || 45,
      aspectRatio,
      this.options.near || 1,
      this.options.far || 2000
    );

    // 调整相机位置，让人物全身居中显示
    this.camera.position.set(0, 0, 300);
  }


  _initRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: this.options.antialias,
      alpha: this.options.alpha
    });

    this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  }


  _initLights() {

    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);


    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 7.5);
    directionalLight.castShadow = true;


    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;

    this.scene.add(directionalLight);

    this.lights = {
      ambient: ambientLight,
      directional: directionalLight
    };
  }


  _initControls() {
    this.controls = new OrbitControls(this.camera, this.canvas);

    // 设置控制器的目标点（人物的中心位置）
    this.controls.target.set(0, 0, 0);

    // 配置控制器参数
    this.controls.enableDamping = true; // 启用阻尼效果
    this.controls.dampingFactor = 0.05; // 阻尼系数
    this.controls.rotateSpeed = 0.5; // 旋转速度
    this.controls.minDistance = 150; // 最小缩放距离
    this.controls.maxDistance = 600; // 最大缩放距离
    this.controls.minPolarAngle = Math.PI / 8; // 最小垂直旋转角度
    this.controls.maxPolarAngle = Math.PI - Math.PI / 8; // 最大垂直旋转角度

    this.controls.update();
  }


  _initResizeHandler() {
    window.addEventListener('resize', this.handleResize.bind(this));
  }


  handleResize() {
    if (!this.canvas) return;

    const width = this.canvas.clientWidth;
    const height = this.canvas.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);

    this.emit('resize', { width, height });
  }


  start() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.clock.start();

    // Ensure proper sizing on start
    this.handleResize();

    this.animate();

    this.emit('start');
  }


  stop() {
    this.isRunning = false;
    this.clock.stop();

    this.emit('stop');
  }

  /**
   * 动画播放，这里注意一定要调用curAnimMixer的update，不然动画没法播放
   * @private
   */
  animate() {
    if (!this.isRunning) return;

    requestAnimationFrame(this.animate.bind(this));

    const delta = this.clock.getDelta();

    if(this.curAnimMixer != null){
      this.curAnimMixer.update(delta);
    }



    if (this.controls) {
      this.controls.update();
    }

    this.renderer.render(this.scene, this.camera);
  }


  add(object) {
    this.scene.add(object);
    this.emit('objectAdded', { object });
  }


  remove(object) {
    this.scene.remove(object);
    this.emit('objectRemoved', { object });
  }


  addAndPlayAnimation(animation, model) {
    if(this.curAnimAction != null && this.curAnimAction.isRunning){
        this.curAnimAction.stop()
    }
    const mixer = new THREE.AnimationMixer(model);
    this.curAnimMixer = mixer;
    this.curAnimAction = this.curAnimMixer.clipAction(animation.animations[0]);
    this.curAnimAction.play();
  }



  dispose() {
    this.stop();

    window.removeEventListener('resize', this.handleResize);


    if (this.controls) {
      this.controls.dispose();
    }


    this.clear();

  }
}
