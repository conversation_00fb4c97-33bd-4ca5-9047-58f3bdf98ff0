import { EventEmitter } from './EventEmitter';
import { AnimationLoader } from '../loader/AnimationLoader';
import { AnimationLoadOptions } from '../types';

export class AnimationManager extends EventEmitter {
    private loader: AnimationLoader;

    constructor() {
        super();
        this.loader = new AnimationLoader();
    }

    async play(animPath: string, options: AnimationLoadOptions = {}): Promise<void> {
        try {
            await this.loader.load(animPath, options);
        } catch (error) {
            console.error('Failed to play animation:', error);
            throw error;
        }
    }

    dispose(): void {
        this.loader.dispose();
        this.clear();
    }
}
