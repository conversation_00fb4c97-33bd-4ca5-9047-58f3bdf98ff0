import * as THREE from 'three';
import { ModelLoader } from "../loader/ModelLoader";
import { EventEmitter } from "./EventEmitter";
import { SceneManager } from "./SceneManager";
import { AnimationLoader } from "../loader/AnimationLoader";


export default class AvatarSDK extends EventEmitter {

    constructor(options = {}) {
        super();
        this.options = {
            debug: false,
            showControls: false,
            ...options
        }
        this.model = null;
        this.initialized = false;
        this.modelLoader = new ModelLoader();
        this.animationLoader = new AnimationLoader();
        this.sceneManager = null;
    }

    async init(canvas) {
        if (this.initialized) {
            console.log("AvatarSDK already initialized");
            return;
        }

        try {
            this.sceneManager = new SceneManager(canvas,
                {
                    debug: this.options.debug,
                    showControls: this.options.showControls
                }
            );
            this.sceneManager.start();
            this.initialized = true;

        } catch (error) {

        }
    }

    async loadModel(url, options = {}) {
        try {
            this.model = await this.modelLoader.load(url, options);
            this.sceneManager.add(this.model);
            console.log("load model success", this.model.position);
            return this.model;
        } catch (error) {
            console.log("load model error", error);
        }
    }

    async playAnimation(url, options = {}) {
        try {
            const animObj = await this.animationLoader.load(url, options);
            console.log("animObj:", animObj);
            if(this.model == null){
                console.log("模型为空,请先加载模型");
                return;
            }
            this.sceneManager.addAndPlayAnimation(animObj,this.model);
        } catch (error) {
            console.log("load anim error", error);
        }
    }


}
