import AvatarSDK from './core/AvatarSDK';
import { BaseLoader } from './loader/BaseLoader';
import { ModelLoader } from './loader/ModelLoader';
import { AnimationLoader } from './loader/AnimationLoader';
import { CustomFBXLoader } from './loader/CustomFBXLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { SceneManager } from './core/SceneManager';
import { EventEmitter } from './core/EventEmitter';
import AvatarView from './components/AvatarView.vue';
import type { 
  AvatarSDKOptions, 
  ModelLoadOptions, 
  AnimationLoadOptions, 
  SceneManagerOptions,
  LoaderOptions 
} from './types';

export function createAvatarSDK(options: AvatarSDKOptions = {}): AvatarSDK {
  const sdk = new AvatarSDK(options);
  return sdk;
}

export {
  AvatarSDK,
  SceneManager,
  EventEmitter,
  BaseLoader,
  ModelLoader,
  AnimationLoader,
  CustomFBXLoader,
  FBXLoader,
  AvatarView,
};

export type {
  AvatarSDKOptions,
  ModelLoadOptions,
  AnimationLoadOptions,
  SceneManagerOptions,
  LoaderOptions,
};
