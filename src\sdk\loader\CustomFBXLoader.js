import { BaseLoader } from './BaseLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
export class CustomFBXLoader extends BaseLoader {

    constructor(options = {}) {
        super();
        this.options = {
            ...options
        }
        this.loader = new FBXLoader();
    }


    async load(path, options = {}) {
        try {
            return new Promise((resolve, reject) => {
                this.loader.load(
                    path,
                    (object) => {
                        resolve(object);
                    },
                    undefined,
                    (error) => {
                        reject(error);
                    }
                );
            });
        } catch (error) {
            throw error;
        }
    }
}