# Avatar SDK

A Three.js based avatar SDK for loading and animating 3D models.

## Features

- 3D model loading (FBX format)
- Animation playback
- Scene management
- Camera controls
- Event system

## Installation

```bash
npm install avatar-sdk
```

## Usage

```javascript
import { createAvatarSDK } from 'avatar-sdk';

// Create SDK instance
const avatarSDK = createAvatarSDK({
    debug: false,
    showControls: true
});

// Initialize with canvas element
await avatarSDK.init(canvasElement);

// Load a model
await avatarSDK.loadModel("path/to/model.fbx");

// Play animation
await avatarSDK.playAnimation("path/to/animation.fbx");
```

## API

### createAvatarSDK(options)

Creates a new AvatarSDK instance.

**Options:**
- `debug` (boolean): Enable debug mode with axis helper
- `showControls` (boolean): Enable orbit controls

### AvatarSDK Methods

- `init(canvas)`: Initialize the SDK with a canvas element
- `loadModel(url, options)`: Load a 3D model
- `playAnimation(url, options)`: Play an animation

## Dependencies

- Three.js ^0.175.0

## License

MIT
