# Avatar SDK

A Three.js based avatar SDK for loading and animating 3D models.

## Features

- 3D model loading (FBX format)
- Animation playback
- Scene management
- Camera controls
- Event system

## Installation

```bash
npm install avatar-sdk
```

## Usage

### Using the Vue Component (Recommended)

```vue
<template>
  <AvatarView
    :debug="true"
    :show-controls="true"
    model-url="path/to/model.fbx"
    animation-url="path/to/animation.fbx"
    @sdk-ready="onSdkReady"
    @model-loaded="onModelLoaded"
    @animation-loaded="onAnimationLoaded"
    @error="onError"
  />
</template>

<script setup>
import { AvatarView } from 'avatar-sdk';

function onSdkReady(sdk) {
  console.log('SDK ready:', sdk);
}

function onModelLoaded(model) {
  console.log('Model loaded:', model);
}

function onAnimationLoaded() {
  console.log('Animation loaded');
}

function onError(error) {
  console.error('Error:', error);
}
</script>
```

### Using the SDK Directly

```javascript
import { createAvatarSDK } from 'avatar-sdk';

// Create SDK instance
const avatarSDK = createAvatarSDK({
    debug: false,
    showControls: true
});

// Initialize with canvas element
await avatarSDK.init(canvasElement);

// Load a model
await avatarSDK.loadModel("path/to/model.fbx");

// Play animation
await avatarSDK.playAnimation("path/to/animation.fbx");
```

## API

### AvatarView Component Props

- `debug` (boolean, default: false): Enable debug mode with axis helper
- `showControls` (boolean, default: true): Enable orbit controls
- `modelUrl` (string): URL to the 3D model file
- `animationUrl` (string): URL to the animation file

### AvatarView Component Events

- `sdk-ready`: Emitted when the SDK is initialized, passes the SDK instance
- `model-loaded`: Emitted when a model is loaded, passes the model object
- `animation-loaded`: Emitted when an animation is loaded
- `error`: Emitted when an error occurs, passes the error object

### AvatarView Component Methods (via ref)

- `loadModel(url)`: Load a 3D model
- `loadAnimation(url)`: Load and play an animation
- `getSDK()`: Get the underlying SDK instance

### createAvatarSDK(options)

Creates a new AvatarSDK instance.

**Options:**
- `debug` (boolean): Enable debug mode with axis helper
- `showControls` (boolean): Enable orbit controls

### AvatarSDK Methods

- `init(canvas)`: Initialize the SDK with a canvas element
- `loadModel(url, options)`: Load a 3D model
- `playAnimation(url, options)`: Play an animation

## Dependencies

- Three.js ^0.175.0
- Vue ^3.5.13 (peer dependency, required when using AvatarView component)

## License

MIT
