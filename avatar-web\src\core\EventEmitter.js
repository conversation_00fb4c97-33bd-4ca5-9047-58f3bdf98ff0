/**
 * 用于AvatarSdk之间的通信
 * 注意这里的events对象的结构
 * {
 *   eventName1: [callback1, callback2, ...],
 *   eventName2: [callback1, callback2, ...]
 * }
 * 这里设计的回调是数组，可以设置多个回调不会覆盖
 */
export class EventEmitter {
  constructor() {
    this.events = {};
  }

  /**
   * 订阅一个事件，如果去需要取消订阅，可以这样做
   * const unsubscribe = emitter.on('eventName', callback);
   * unsubscribe();
   * 这样就能取消订阅
   * @param {string} event - 事件的名称
   * @param {Function} callback - 事件的回调函数
   * @returns {Function} 取消订阅的函数
   */
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    
    this.events[event].push(callback);
    
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    };
  }

  /**
   * 这个用来一次性的订阅，同时也支持了返回一个取消的函数，可以在回调前手动取消
   * @param {string} event - 事件的名称
   * @param {Function} callback - 事件的回调函数
   * @returns {Function} 取消订阅的函数
   */
  once(event, callback) {
    const unsubscribe = this.on(event, (...args) => {
      //回调之后立马取消订阅
      unsubscribe();
      //执行回调
      callback(...args);
    });
    
    return unsubscribe;
  }

  /**
   * 触发一个事件
   * @param {string} event - 事件的名称
   * @param {...any} args - 传递给回调函数的参数
   */
  emit(event, ...args) {
    if (this.events[event]) {
      this.events[event].forEach(callback => {
        callback(...args);
      });
    }
  }

  /**
   * 移除所有的事件监听
   * @param {string} [event] - 可选的事件名称，只清除特定的事件
   */
  clear(event) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}
