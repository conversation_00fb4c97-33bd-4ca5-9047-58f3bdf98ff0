<script setup>
import { ref } from 'vue';
import { AvatarView } from 'avatar-web';
const debug = ref(true);
const showControls = ref(true);
const modelUrl = ref("models/Taunt.fbx");
const animationUrl = ref("anim/Locking Hip Hop Dance.fbx");

function onSdkReady(sdk) {
  console.log('Avatar SDK ready:', sdk);
}

function onModelLoaded(model) {
  console.log('Model loaded:', model);
}

function onAnimationLoaded() {
  console.log('Animation loaded');
}

function onError(error) {
  console.error('Avatar error:', error);
}

</script>

<template>
  <div id="app">
    <div class="app-header">
      <h2 style="margin: 0; font-size: 1.25rem;">Avatar 示例 Demo</h2>
    </div>
    <AvatarView
      class="viewer-container"
      :debug="debug"
      :show-controls="showControls"
      :model-url="modelUrl"
      :animation-url="animationUrl"
      @sdk-ready="onSdkReady"
      @model-loaded="onModelLoaded"
      @animation-loaded="onAnimationLoaded"
      @error="onError"
    ></AvatarView>
  </div>
</template>

<style scoped>
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 0.5rem;
  text-align: center;
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}



.viewer-container {
  flex: 1;
  position: relative;
}
</style>
