import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { BaseLoader } from './BaseLoader';

/**
 * 模型加载，暂时用FBXLoader去测试，后续模型文件格式确认后在修改
 */
export class ModelLoader extends BaseLoader {

    constructor() {
        super();
        this.loader = new FBXLoader();
    }


    async load(path, options = {}) {
        const mergedOptions = {
            scale: 1,
            position: new THREE.Vector3(0, -75, 0),
            rotation: new THREE.Euler(0, 0, 0),
            ...this.options,
            ...options
        };

        try {
            return new Promise((resolve, reject) => {
                this.loader.load(
                    path,
                    (object) => {
                        this.processModel(object, mergedOptions);
                        resolve(object);
                    },
                    undefined,
                    (error) => {
                        console.log("load model fail ", path, " ", error);
                        reject(error);
                    }
                );
            });
        } catch (error) {
            this.emit('loadError', { url, error });
            console.log("load model ", path, " ", error);
            throw error;
        }
    }


    /**
      * @param {THREE.Object3D} model
      * @param {Object} options
      * @private
      */
    processModel(model, options) {
        if (options.scale) {
            if (typeof options.scale === 'number') {
                model.scale.set(options.scale, options.scale, options.scale);
            } else if (options.scale instanceof THREE.Vector3) {
                model.scale.copy(options.scale);
            }
        }

        if (options.position) {
            model.position.copy(options.position);
        }

        if (options.rotation) {
            model.rotation.copy(options.rotation);
        }
        return model;
    }

}
