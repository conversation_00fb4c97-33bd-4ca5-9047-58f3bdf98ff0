{"name": "avatar-web", "version": "1.0.0", "description": "A Three.js based avatar web library for loading and animating 3D models", "main": "dist/avatar-web.umd.js", "module": "dist/avatar-web.es.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": ["avatar", "3d", "threejs", "animation", "fbx"], "author": "", "license": "MIT", "dependencies": {"three": "^0.175.0"}, "devDependencies": {"vite": "^6.2.4", "@vitejs/plugin-vue": "^5.2.3"}, "peerDependencies": {"three": "^0.175.0", "vue": "^3.5.13"}}