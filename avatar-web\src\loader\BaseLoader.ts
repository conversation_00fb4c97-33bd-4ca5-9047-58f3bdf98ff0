import * as THREE from 'three';
import { EventEmitter } from '../core/EventEmitter';
import { LoaderOptions } from '../types';

/**
 * 资源loader的基类
 */
export abstract class BaseLoader extends EventEmitter {
    protected options: LoaderOptions;
    
    constructor(options: LoaderOptions = {}) {
        super();
        this.options = options;
    }

    /**
     * 根据路径去加载
     * @param path - 资源路径
     * @param options - 加载选项
     * @returns Loaded model
     */
    abstract load(path: string, options?: LoaderOptions): Promise<THREE.Object3D>;

    /**
     * 解析数据
     * @param data - 数据
     * @param options - 解析选项
     * @returns 解析后的对象
     */
    abstract parse(data: ArrayBuffer | string, options?: LoaderOptions): Promise<THREE.Object3D>;

    /**
     * Clean up resources
     */
    dispose(): void {
        this.clear();
    }
}
