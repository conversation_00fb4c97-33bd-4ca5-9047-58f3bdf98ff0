declare module 'three/examples/jsm/controls/OrbitControls' {
  import { Camera, EventDispatcher, MOUSE, TOUCH, Vector3 } from 'three';

  export class OrbitControls extends EventDispatcher {
    constructor(object: Camera, domElement?: HTMLElement);
    
    object: Camera;
    domElement: HTMLElement | Document;
    
    // API
    enabled: boolean;
    target: Vector3;
    
    // Damping
    enableDamping: boolean;
    dampingFactor: number;
    
    // Rotation
    enableRotate: boolean;
    rotateSpeed: number;
    
    // Zoom
    enableZoom: boolean;
    zoomSpeed: number;
    minDistance: number;
    maxDistance: number;
    
    // Pan
    enablePan: boolean;
    panSpeed: number;
    
    // Polar angle limits
    minPolarAngle: number;
    maxPolarAngle: number;
    
    // Azimuth angle limits
    minAzimuthAngle: number;
    maxAzimuthAngle: number;
    
    // Mouse buttons
    mouseButtons: {
      LEFT?: MOUSE;
      MIDDLE?: MOUSE;
      RIGHT?: MOUSE;
    };
    
    // Touch fingers
    touches: {
      ONE?: TOUCH;
      TWO?: TOUCH;
    };
    
    update(): boolean;
    dispose(): void;
  }
}

declare module 'three/examples/jsm/loaders/FBXLoader' {
  import { Group, Loader, LoadingManager } from 'three';

  export class FBXLoader extends Loader {
    constructor(manager?: LoadingManager);
    
    load(
      url: string,
      onLoad?: (object: Group) => void,
      onProgress?: (event: ProgressEvent) => void,
      onError?: (event: ErrorEvent) => void
    ): void;
    
    parse(buffer: ArrayBuffer | string, path?: string): Group;
  }
}
