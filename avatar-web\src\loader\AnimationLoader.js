import { BaseLoader } from './BaseLoader';
import { CustomFBXLoader } from './CustomFBXLoader';
export class AnimationLoader extends BaseLoader {
    constructor(options = {}) {
        super();
        this.options = {
            ...options
        }
        this.loader = new CustomFBXLoader();
    }

    async load(path, options = {}) {
        try {
            const animObj = await this.loader.load(path, options);
            return animObj;
        } catch (error) {
            throw error;
        }
        
    }
}
