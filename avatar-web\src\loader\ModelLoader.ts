import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { BaseLoader } from './BaseLoader';
import { ModelLoadOptions } from '../types';

/**
 * 模型加载，暂时用FBXLoader去测试，后续模型文件格式确认后在修改
 */
export class ModelLoader extends BaseLoader {
    private loader: FBXLoader;

    constructor(options: ModelLoadOptions = {}) {
        super(options);
        this.loader = new FBXLoader();
    }

    async load(path: string, options: ModelLoadOptions = {}): Promise<THREE.Object3D> {
        const mergedOptions: ModelLoadOptions = {
            scale: 1,
            position: new THREE.Vector3(0, -75, 0),
            rotation: new THREE.Euler(0, 0, 0),
            ...this.options,
            ...options
        };

        try {
            return new Promise((resolve, reject) => {
                this.loader.load(
                    path,
                    (object: THREE.Object3D) => {
                        this.processModel(object, mergedOptions);
                        resolve(object);
                    },
                    undefined,
                    (error: any) => {
                        console.log("load model fail ", path, " ", error);
                        reject(error);
                    }
                );
            });
        } catch (error) {
            this.emit('loadError', { path, error });
            console.log("load model ", path, " ", error);
            throw error;
        }
    }

    async parse(_data: ArrayBuffer | string, _options: ModelLoadOptions = {}): Promise<THREE.Object3D> {
        // FBXLoader doesn't have a direct parse method for ArrayBuffer
        // This would need to be implemented based on the specific loader
        throw new Error('Parse method not implemented for ModelLoader');
    }

    /**
     * 处理模型的位置、缩放、旋转等属性
     * @param model - 3D模型对象
     * @param options - 处理选项
     * @private
     */
    private processModel(model: THREE.Object3D, options: ModelLoadOptions): THREE.Object3D {
        if (options.scale !== undefined) {
            if (typeof options.scale === 'number') {
                model.scale.set(options.scale, options.scale, options.scale);
            } else {
                // options.scale is THREE.Vector3
                model.scale.copy(options.scale);
            }
        }

        if (options.position) {
            model.position.copy(options.position);
        }

        if (options.rotation) {
            model.rotation.copy(options.rotation);
        }
        
        return model;
    }
}
