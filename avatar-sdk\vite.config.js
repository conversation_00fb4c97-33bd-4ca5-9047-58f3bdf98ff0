import { defineConfig } from 'vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.js'),
      name: 'AvatarSDK',
      fileName: (format) => `avatar-sdk.${format}.js`
    },
    rollupOptions: {
      external: ['three', 'vue'],
      output: {
        globals: {
          three: 'THREE',
          vue: 'Vue'
        }
      }
    }
  }
})
