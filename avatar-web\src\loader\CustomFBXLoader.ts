import * as THREE from 'three';
import { BaseLoader } from './BaseLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { LoaderOptions } from '../types';

export class CustomFBXLoader extends BaseLoader {
    private loader: FBXLoader;

    constructor(options: LoaderOptions = {}) {
        super(options);
        this.loader = new FBXLoader();
    }

    async load(path: string, options?: LoaderOptions): Promise<THREE.Object3D> {
        console.log("CustomFBXLoader options ", options);
        try {
            return new Promise((resolve, reject) => {
                this.loader.load(
                    path,
                    (object: THREE.Object3D) => {
                        resolve(object);
                    },
                    undefined,
                    (error: any) => {
                        reject(error);
                    }
                );
            });
        } catch (error) {
            throw error;
        }
    }

    async parse(_data: ArrayBuffer | string, _options: LoaderOptions = {}): Promise<THREE.Object3D> {
        // FBXLoader doesn't have a direct parse method for ArrayBuffer
        // This would need to be implemented based on the specific loader
        throw new Error('Parse method not implemented for CustomFBXLoader');
    }
}
