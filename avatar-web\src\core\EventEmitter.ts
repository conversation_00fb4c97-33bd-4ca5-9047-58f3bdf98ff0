import { EventCallback, EventMap } from '../types';

/**
 * 用于AvatarSdk之间的通信
 * 注意这里的events对象的结构
 * {
 *   eventName1: [callback1, callback2, ...],
 *   eventName2: [callback1, callback2, ...]
 * }
 * 这里设计的回调是数组，可以设置多个回调不会覆盖
 */
export class EventEmitter {
  private events: EventMap = {};

  /**
   * 订阅一个事件，如果去需要取消订阅，可以这样做
   * const unsubscribe = emitter.on('eventName', callback);
   * unsubscribe();
   * 这样就能取消订阅
   * @param event - 事件的名称
   * @param callback - 事件的回调函数
   * @returns 取消订阅的函数
   */
  on(event: string, callback: EventCallback): () => void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    
    this.events[event].push(callback);
    
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    };
  }

  /**
   * 这个用来一次性的订阅，同时也支持了返回一个取消的函数，可以在回调前手动取消
   * @param event - 事件的名称
   * @param callback - 事件的回调函数
   * @returns 取消订阅的函数
   */
  once(event: string, callback: EventCallback): () => void {
    const unsubscribe = this.on(event, (...args: any[]) => {
      //回调之后立马取消订阅
      unsubscribe();
      //执行回调
      callback(...args);
    });
    
    return unsubscribe;
  }

  /**
   * 触发一个事件
   * @param event - 事件的名称
   * @param args - 传递给回调函数的参数
   */
  emit(event: string, ...args: any[]): void {
    if (this.events[event]) {
      this.events[event].forEach(callback => {
        callback(...args);
      });
    }
  }

  /**
   * 移除所有的事件监听
   * @param event - 可选的事件名称，只清除特定的事件
   */
  clear(event?: string): void {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}
