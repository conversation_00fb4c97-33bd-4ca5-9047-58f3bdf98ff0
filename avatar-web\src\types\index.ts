import * as THREE from 'three';

export interface AvatarSDKOptions {
  debug?: boolean;
  showControls?: boolean;
  fov?: number;
  near?: number;
  far?: number;
  antialias?: boolean;
  alpha?: boolean;
  autoResize?: boolean;
}

export interface LoaderOptions {
  scale?: number | THREE.Vector3;
  position?: THREE.Vector3;
  rotation?: THREE.Euler;
}

export interface ModelLoadOptions extends LoaderOptions {
  // Model specific options can be added here
}

export interface AnimationLoadOptions extends LoaderOptions {
  loop?: boolean;
  timeScale?: number;
  crossFadeDuration?: number;
}

export interface SceneManagerOptions {
  antialias?: boolean;
  alpha?: boolean;
  autoResize?: boolean;
  debug?: boolean;
  showControls?: boolean;
  fov?: number;
  near?: number;
  far?: number;
}

export interface EventData {
  [key: string]: any;
}

export type EventCallback = (data?: EventData) => void;

export interface EventMap {
  [eventName: string]: EventCallback[];
}
